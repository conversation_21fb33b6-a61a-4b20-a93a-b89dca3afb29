#!/usr/bin/env python3
"""
تشغيل البوت مع ميزة إزالة العلامة المائية
"""

import os
import sys
import subprocess
import time

def print_banner():
    """طباعة شعار البوت"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🤖 بوت سحب المنشورات مع إزالة العلامة المائية 🎬        ║
║                                                              ║
║    ✨ ميزات جديدة:                                          ║
║    • إزالة العلامة المائية من الفيديوهات                   ║
║    • كشف تلقائي للعلامات المائية                           ║
║    • طريقتان للمعالجة (طمس + إزالة ذكية)                  ║
║    • واجهة إدارية متقدمة                                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_environment():
    """فحص البيئة والملفات المطلوبة"""
    print("🔍 فحص البيئة...")
    
    # فحص ملف .env
    if not os.path.exists('.env'):
        print("❌ ملف .env غير موجود")
        print("💡 أنشئ ملف .env وأضف:")
        print("   API_ID=your_api_id")
        print("   API_HASH=your_api_hash")
        print("   BOT_TOKEN=your_bot_token")
        print("   ADMIN_IDS=your_admin_id")
        return False
    
    # فحص الملفات الأساسية
    required_files = ['bot.py', 'video_watermark_remover.py', 'vip_membership_system.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ ملف {file} غير موجود")
            return False
        else:
            print(f"✅ {file}")
    
    return True

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات...")
    
    # المكتبات الأساسية
    basic_deps = {
        'pyrogram': 'pyrogram',
        'python-dotenv': 'dotenv',
        'tgcrypto': 'tgcrypto'
    }
    
    # مكتبات العلامة المائية
    watermark_deps = {
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'Pillow': 'PIL',
        'moviepy': 'moviepy'
    }
    
    missing_basic = []
    missing_watermark = []
    
    # فحص المكتبات الأساسية
    for package, module in basic_deps.items():
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_basic.append(package)
    
    # فحص مكتبات العلامة المائية
    for package, module in watermark_deps.items():
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"⚠️ {package} (للعلامة المائية)")
            missing_watermark.append(package)
    
    if missing_basic:
        print(f"\n❌ مكتبات أساسية مفقودة: {', '.join(missing_basic)}")
        print("💡 قم بتثبيتها: pip install " + " ".join(missing_basic))
        return False
    
    if missing_watermark:
        print(f"\n⚠️ مكتبات العلامة المائية مفقودة: {', '.join(missing_watermark)}")
        print("💡 لتفعيل ميزة إزالة العلامة المائية:")
        print("   pip install " + " ".join(missing_watermark))
        print("🔄 البوت سيعمل بدون هذه الميزة")
    
    return True

def install_missing_dependencies():
    """تثبيت المكتبات المفقودة"""
    print("\n📥 هل تريد تثبيت المكتبات المفقودة؟ (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes', 'نعم']:
        print("⏳ تثبيت المكتبات...")
        try:
            # تثبيت المكتبات الأساسية
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ تم تثبيت المكتبات بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في التثبيت: {e}")
            return False
    
    return False

def show_admin_commands():
    """عرض أوامر المشرفين"""
    commands = """
🛡️ أوامر المشرفين المتاحة:

📊 الإدارة العامة:
/start - بدء البوت وعرض القائمة الرئيسية
/help - عرض دليل الاستخدام
/stats - إحصائيات البوت
/manage_vip - إدارة أعضاء VIP

🎬 إدارة العلامة المائية:
/watermark_settings - عرض وتعديل إعدادات العلامة المائية
/toggle_watermark - تفعيل/تعطيل ميزة إزالة العلامة المائية
/watermark_method blur - تغيير طريقة الإزالة للطمس
/watermark_method inpaint - تغيير طريقة الإزالة للإزالة الذكية
/toggle_auto_process - تفعيل/تعطيل المعالجة التلقائية
/test_watermark - اختبار ميزة إزالة العلامة المائية

💡 نصائح:
• استخدم الأزرار التفاعلية في لوحة التحكم للوصول السريع
• طريقة "blur" أسرع لكن طريقة "inpaint" أفضل جودة
• تأكد من تفعيل المعالجة التلقائية لمعالجة جميع الفيديوهات
"""
    print(commands)

def run_bot():
    """تشغيل البوت"""
    print("\n🚀 بدء تشغيل البوت...")
    print("=" * 60)
    print("💡 للإيقاف: اضغط Ctrl+C")
    print("🔧 للوصول لإعدادات العلامة المائية: /watermark_settings")
    print("=" * 60)
    
    try:
        # تشغيل البوت
        subprocess.run([sys.executable, "bot.py"])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")

def main():
    """الوظيفة الرئيسية"""
    print_banner()
    
    # فحص البيئة
    if not check_environment():
        print("\n❌ فشل في فحص البيئة")
        return
    
    # فحص المكتبات
    if not check_dependencies():
        if install_missing_dependencies():
            print("✅ تم تثبيت المكتبات، يمكنك الآن تشغيل البوت")
        else:
            print("❌ لا يمكن تشغيل البوت بدون المكتبات الأساسية")
            return
    
    # عرض أوامر المشرفين
    print("\n" + "=" * 60)
    show_admin_commands()
    print("=" * 60)
    
    # تشغيل البوت
    run_bot()

if __name__ == "__main__":
    main()
