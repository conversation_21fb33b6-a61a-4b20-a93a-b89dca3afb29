"""
نظام إزالة العلامة المائية من الفيديوهات
يدعم إزالة العلامات المائية الشائعة من الفيديوهات
"""

import cv2
import numpy as np
import os
import tempfile
import logging
from typing import Optional, Tuple, List
from moviepy.editor import VideoFileClip
from PIL import Image, ImageFilter

# استيراد الإعدادات المتقدمة
try:
    from watermark_config import (
        DETECTION_SETTINGS, PROCESSING_SETTINGS, PERFORMANCE_SETTINGS,
        get_detection_regions, get_processing_config, optimize_settings_for_video
    )
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    # إعدادات افتراضية في حالة عدم وجود ملف التكوين
    DETECTION_SETTINGS = {
        "min_variance_threshold": 100,
        "detection_regions": {
            "top_right": (0.7, 0, 1.0, 0.3),
            "top_left": (0, 0, 0.3, 0.3),
            "bottom_right": (0.7, 0.7, 1.0, 1.0),
            "bottom_left": (0, 0.7, 0.3, 1.0),
            "top_center": (0.3, 0, 0.7, 0.2),
            "bottom_center": (0.3, 0.8, 0.7, 1.0)
        }
    }

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WatermarkRemover:
    """فئة لإزالة العلامات المائية من الفيديوهات"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        
    def detect_watermark_region(self, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        كشف منطقة العلامة المائية في الإطار باستخدام الإعدادات المتقدمة
        """
        try:
            # تحويل إلى رمادي للمعالجة
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape

            # الحصول على المناطق من الإعدادات
            if CONFIG_AVAILABLE:
                detection_regions = get_detection_regions(w, h)
                min_variance = DETECTION_SETTINGS["min_variance_threshold"]
            else:
                # إعدادات افتراضية
                detection_regions = {
                    "top_right": (int(w*0.7), 0, w, int(h*0.3)),
                    "top_left": (0, 0, int(w*0.3), int(h*0.3)),
                    "bottom_right": (int(w*0.7), int(h*0.7), w, h),
                    "bottom_left": (0, int(h*0.7), int(w*0.3), h),
                    "top_center": (int(w*0.3), 0, int(w*0.7), int(h*0.2)),
                    "bottom_center": (int(w*0.3), int(h*0.8), int(w*0.7), h)
                }
                min_variance = 100

            best_region = None
            max_score = 0

            for region_name, (x1, y1, x2, y2) in detection_regions.items():
                roi = gray[y1:y2, x1:x2]
                if roi.size > 0:
                    # حساب التباين في المنطقة
                    variance = np.var(roi)

                    # حساب نقاط إضافية للكشف المتقدم
                    score = variance

                    # إضافة كشف الحواف إذا كان متاحاً
                    if CONFIG_AVAILABLE:
                        try:
                            from watermark_config import ADVANCED_DETECTION
                            if ADVANCED_DETECTION.get("use_edge_detection", False):
                                edges = cv2.Canny(roi, 50, 150)
                                edge_density = np.sum(edges > 0) / roi.size
                                score += edge_density * 1000  # تعزيز النقاط للحواف
                        except:
                            pass

                    # البحث عن المناطق ذات النقاط العالية
                    if score > max_score and variance > min_variance:
                        max_score = score
                        best_region = (x1, y1, x2, y2)

            if best_region and CONFIG_AVAILABLE:
                try:
                    from watermark_config import PERFORMANCE_SETTINGS
                    if PERFORMANCE_SETTINGS.get("log_detection_results", False):
                        logger.info(f"تم كشف علامة مائية بنقاط: {max_score:.2f} في المنطقة: {best_region}")
                except:
                    pass

            return best_region

        except Exception as e:
            logger.error(f"خطأ في كشف العلامة المائية: {e}")
            return None
    
    def remove_watermark_inpainting(self, frame: np.ndarray, region: Tuple[int, int, int, int]) -> np.ndarray:
        """
        إزالة العلامة المائية باستخدام تقنية Inpainting المتقدمة
        """
        try:
            x1, y1, x2, y2 = region

            # الحصول على إعدادات الإزالة الذكية
            if CONFIG_AVAILABLE:
                inpaint_config = get_processing_config("inpaint")
                radius = inpaint_config.get("radius", 3)
                method_name = inpaint_config.get("method", "TELEA")

                # تحديد طريقة الإزالة
                if method_name == "NS":
                    method = cv2.INPAINT_NS
                else:
                    method = cv2.INPAINT_TELEA
            else:
                radius = 3
                method = cv2.INPAINT_TELEA

            # إنشاء قناع للمنطقة المراد إزالتها
            mask = np.zeros(frame.shape[:2], dtype=np.uint8)
            mask[y1:y2, x1:x2] = 255

            # توسيع القناع قليلاً للحصول على نتائج أفضل
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.dilate(mask, kernel, iterations=1)

            # استخدام تقنية Inpainting لملء المنطقة
            result = cv2.inpaint(frame, mask, radius, method)

            return result

        except Exception as e:
            logger.error(f"خطأ في إزالة العلامة المائية: {e}")
            return frame
    
    def blur_watermark_region(self, frame: np.ndarray, region: Tuple[int, int, int, int]) -> np.ndarray:
        """
        طمس منطقة العلامة المائية باستخدام الإعدادات المتقدمة
        """
        try:
            x1, y1, x2, y2 = region

            # الحصول على إعدادات الطمس
            if CONFIG_AVAILABLE:
                blur_config = get_processing_config("blur")
                kernel_size = blur_config.get("kernel_size", (15, 15))
                sigma_x = blur_config.get("sigma_x", 0)
                sigma_y = blur_config.get("sigma_y", 0)
            else:
                kernel_size = (15, 15)
                sigma_x = 0
                sigma_y = 0

            # نسخ الإطار
            result = frame.copy()

            # استخراج المنطقة وطمسها
            roi = result[y1:y2, x1:x2]
            blurred_roi = cv2.GaussianBlur(roi, kernel_size, sigma_x, sigmaY=sigma_y)

            # استبدال المنطقة بالنسخة المطموسة
            result[y1:y2, x1:x2] = blurred_roi

            return result

        except Exception as e:
            logger.error(f"خطأ في طمس العلامة المائية: {e}")
            return frame
    
    def process_video(self, input_path: str, output_path: str, method: str = "inpaint") -> bool:
        """
        معالجة الفيديو وإزالة العلامة المائية مع التحسينات المتقدمة

        Args:
            input_path: مسار الفيديو الأصلي
            output_path: مسار الفيديو المعالج
            method: طريقة الإزالة ("inpaint" أو "blur")
        """
        try:
            import time
            start_time = time.time()

            # فتح الفيديو
            cap = cv2.VideoCapture(input_path)

            if not cap.isOpened():
                logger.error("فشل في فتح الفيديو")
                return False

            # الحصول على خصائص الفيديو
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # تحسين الإعدادات حسب خصائص الفيديو
            if CONFIG_AVAILABLE:
                file_size = os.path.getsize(input_path)
                duration = total_frames / fps if fps > 0 else 0
                optimized_settings = optimize_settings_for_video(width, height, duration, file_size)

                # استخدام الطريقة المحسنة إذا كانت متاحة
                if "method" in optimized_settings:
                    method = optimized_settings["method"]

                # الحصول على إعدادات الأداء
                from watermark_config import PERFORMANCE_SETTINGS
                max_processing_time = PERFORMANCE_SETTINGS.get("max_processing_time", 300)
                progress_interval = PERFORMANCE_SETTINGS.get("progress_update_interval", 30)
            else:
                max_processing_time = 300
                progress_interval = 30

            # إعداد كاتب الفيديو
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            # قراءة الإطار الأول لكشف العلامة المائية
            ret, first_frame = cap.read()
            if not ret:
                logger.error("فشل في قراءة الإطار الأول")
                return False

            # كشف منطقة العلامة المائية
            watermark_region = self.detect_watermark_region(first_frame)
            
            if watermark_region is None:
                logger.info("لم يتم العثور على علامة مائية واضحة")
                # نسخ الفيديو كما هو
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                while True:
                    # فحص الوقت المنقضي
                    if time.time() - start_time > max_processing_time:
                        logger.warning("تم تجاوز الحد الأقصى لوقت المعالجة")
                        break

                    ret, frame = cap.read()
                    if not ret:
                        break
                    out.write(frame)
            else:
                logger.info(f"تم العثور على علامة مائية في المنطقة: {watermark_region}")

                # إعادة تعيين موضع الفيديو للبداية
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)

                frame_count = 0
                while True:
                    # فحص الوقت المنقضي
                    if time.time() - start_time > max_processing_time:
                        logger.warning("تم تجاوز الحد الأقصى لوقت المعالجة")
                        break

                    ret, frame = cap.read()
                    if not ret:
                        break

                    # معالجة الإطار
                    if method == "inpaint":
                        processed_frame = self.remove_watermark_inpainting(frame, watermark_region)
                    else:  # blur
                        processed_frame = self.blur_watermark_region(frame, watermark_region)

                    out.write(processed_frame)

                    frame_count += 1
                    if frame_count % progress_interval == 0:  # تحديث حسب الإعدادات
                        progress = (frame_count / total_frames) * 100
                        elapsed_time = time.time() - start_time
                        logger.info(f"تقدم المعالجة: {progress:.1f}% - الوقت المنقضي: {elapsed_time:.1f}s")

            # تنظيف الموارد
            cap.release()
            out.release()

            # تسجيل إحصائيات الأداء
            total_time = time.time() - start_time
            if CONFIG_AVAILABLE:
                try:
                    from watermark_config import PERFORMANCE_SETTINGS
                    if PERFORMANCE_SETTINGS.get("log_processing_time", False):
                        logger.info(f"وقت المعالجة الإجمالي: {total_time:.2f} ثانية")
                    if PERFORMANCE_SETTINGS.get("log_file_sizes", False):
                        input_size = os.path.getsize(input_path)
                        output_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
                        logger.info(f"حجم الملف الأصلي: {input_size / 1024:.1f} KB")
                        logger.info(f"حجم الملف المعالج: {output_size / 1024:.1f} KB")
                except:
                    pass

            logger.info("تم الانتهاء من معالجة الفيديو بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الفيديو: {e}")
            return False
    
    def process_video_with_audio(self, input_path: str, output_path: str, method: str = "inpaint") -> bool:
        """
        معالجة الفيديو مع الحفاظ على الصوت
        """
        try:
            # إنشاء ملف مؤقت للفيديو بدون صوت
            temp_video = os.path.join(self.temp_dir, "temp_video.mp4")
            
            # معالجة الفيديو
            if not self.process_video(input_path, temp_video, method):
                return False
            
            # استخراج الصوت من الفيديو الأصلي ودمجه مع الفيديو المعالج
            try:
                original_clip = VideoFileClip(input_path)
                processed_clip = VideoFileClip(temp_video)
                
                # دمج الصوت
                if original_clip.audio is not None:
                    final_clip = processed_clip.set_audio(original_clip.audio)
                    final_clip.write_videofile(output_path, codec='libx264', audio_codec='aac')
                    final_clip.close()
                else:
                    # لا يوجد صوت، نسخ الفيديو المعالج فقط
                    processed_clip.write_videofile(output_path, codec='libx264')
                
                original_clip.close()
                processed_clip.close()
                
                # حذف الملف المؤقت
                if os.path.exists(temp_video):
                    os.remove(temp_video)
                
                return True
                
            except Exception as e:
                logger.error(f"خطأ في معالجة الصوت: {e}")
                # في حالة فشل معالجة الصوت، نسخ الفيديو بدون صوت
                if os.path.exists(temp_video):
                    os.rename(temp_video, output_path)
                return True
                
        except Exception as e:
            logger.error(f"خطأ في معالجة الفيديو مع الصوت: {e}")
            return False
    
    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المؤقتة: {e}")

# مثيل عام للاستخدام
watermark_remover = WatermarkRemover()

def remove_video_watermark(input_path: str, output_path: str, method: str = "blur") -> bool:
    """
    وظيفة مبسطة لإزالة العلامة المائية من الفيديو
    
    Args:
        input_path: مسار الفيديو الأصلي
        output_path: مسار الفيديو المعالج  
        method: طريقة الإزالة ("inpaint" أو "blur")
    
    Returns:
        bool: True إذا نجحت العملية، False إذا فشلت
    """
    return watermark_remover.process_video_with_audio(input_path, output_path, method)
