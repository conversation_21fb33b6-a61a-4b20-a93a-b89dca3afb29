#!/usr/bin/env python3
"""
دليل البدء السريع لميزة إزالة العلامة المائية
"""

import os
import sys
import subprocess

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🎬 دليل البدء السريع - ميزة إزالة العلامة المائية")
    print("=" * 60)

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    print("\n📦 تثبيت المكتبات المطلوبة...")
    
    try:
        # تشغيل سكريبت التثبيت
        result = subprocess.run([sys.executable, "install_watermark_dependencies.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع المكتبات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت بعض المكتبات")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("⚠️ ملف التثبيت غير موجود، محاولة التثبيت اليدوي...")
        
        packages = ["opencv-python", "numpy", "Pillow", "moviepy"]
        
        for package in packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package}")
            except subprocess.CalledProcessError:
                print(f"❌ {package}")
                return False
        
        return True

def run_tests():
    """تشغيل الاختبارات"""
    print("\n🧪 تشغيل الاختبارات...")
    
    try:
        result = subprocess.run([sys.executable, "test_watermark_removal.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ جميع الاختبارات نجحت")
            print(result.stdout)
            return True
        else:
            print("❌ بعض الاختبارات فشلت")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("⚠️ ملف الاختبار غير موجود")
        return False

def check_bot_files():
    """فحص ملفات البوت المطلوبة"""
    print("\n📁 فحص ملفات البوت...")
    
    required_files = [
        "bot.py",
        "video_watermark_remover.py",
        "watermark_config.py",
        "requirements.txt",
        ".env"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def show_configuration_guide():
    """عرض دليل التكوين"""
    print("\n⚙️ دليل التكوين:")
    print("-" * 40)
    
    print("1. تحديث ملف .env:")
    print("   - أضف API_ID و API_HASH من my.telegram.org")
    print("   - أضف BOT_TOKEN من @BotFather")
    print("   - أضف ADMIN_IDS (اختياري)")
    
    print("\n2. تخصيص إعدادات العلامة المائية:")
    print("   - عدل ملف watermark_config.py حسب احتياجاتك")
    print("   - اضبط WATERMARK_REMOVAL_SETTINGS في bot.py")
    
    print("\n3. اختبار الميزة:")
    print("   - استخدم /test_watermark في البوت")
    print("   - أرسل فيديو للبوت لاختبار المعالجة التلقائية")

def show_usage_examples():
    """عرض أمثلة الاستخدام"""
    print("\n📝 أمثلة الاستخدام:")
    print("-" * 40)
    
    print("للمستخدمين العاديين:")
    print("1. ابدأ محادثة مع البوت")
    print("2. اختر 'سحب منشور واحد'")
    print("3. أرسل رابط فيديو يحتوي على علامة مائية")
    print("4. احصل على الفيديو مع إزالة العلامة المائية")
    
    print("\nللمشرفين:")
    print("- /watermark_settings - عرض الإعدادات")
    print("- /toggle_watermark - تفعيل/تعطيل الميزة")
    print("- /watermark_method blur - تغيير للطمس")
    print("- /watermark_method inpaint - تغيير للإزالة الذكية")
    print("- /test_watermark - اختبار الميزة")

def show_troubleshooting():
    """عرض دليل استكشاف الأخطاء"""
    print("\n🔧 استكشاف الأخطاء الشائعة:")
    print("-" * 40)
    
    print("1. خطأ في تحميل المكتبات:")
    print("   - تأكد من تثبيت جميع المكتبات المطلوبة")
    print("   - جرب: pip install --upgrade opencv-python")
    
    print("\n2. الميزة لا تعمل:")
    print("   - تحقق من رسائل السجل")
    print("   - تأكد من تفعيل الميزة في الإعدادات")
    
    print("\n3. جودة المعالجة ضعيفة:")
    print("   - جرب تغيير الطريقة من blur إلى inpaint")
    print("   - تأكد من أن العلامة المائية في إحدى الزوايا")
    
    print("\n4. الفيديو كبير جداً:")
    print("   - الحد الأقصى افتراضياً: 50 ميجابايت")
    print("   - يمكن تعديله في WATERMARK_REMOVAL_SETTINGS")

def main():
    """الوظيفة الرئيسية"""
    print_header()
    
    # فحص إصدار Python
    if not check_python_version():
        return
    
    # فحص ملفات البوت
    if not check_bot_files():
        print("\n❌ بعض الملفات المطلوبة مفقودة")
        print("💡 تأكد من وجود جميع ملفات البوت في نفس المجلد")
        return
    
    # تثبيت المكتبات
    if not install_dependencies():
        print("\n❌ فشل في تثبيت المكتبات المطلوبة")
        print("💡 جرب التثبيت اليدوي:")
        print("   pip install opencv-python numpy Pillow moviepy")
        return
    
    # تشغيل الاختبارات
    if not run_tests():
        print("\n⚠️ بعض الاختبارات فشلت، لكن يمكن المتابعة")
    
    # عرض الأدلة
    show_configuration_guide()
    show_usage_examples()
    show_troubleshooting()
    
    print("\n" + "=" * 60)
    print("🎉 الإعداد مكتمل!")
    print("🚀 يمكنك الآن تشغيل البوت: python bot.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
