"""
نظام العضوية المميزة (VIP) - ملف منفصل قابل للاستخدام في مشاريع أخرى

هذا الملف يحتوي على نظام كامل لإدارة العضوية المميزة مع:
- إدارة أعضاء VIP
- التحقق من الصلاحيات
- تفعيل وإلغاء العضوية
- تمديد الاشتراكات
- تنظيف العضويات المنتهية
- واجهات إدارية متقدمة

المطلوب للاستخدام:
- python-telegram-bot
- json
- datetime
- os
- logging
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class VIPManager:
    """مدير نظام العضوية المميزة (VIP)"""
    
    def __init__(self, vip_file: str = "vip_users.json"):
        """
        تهيئة مدير VIP
        
        Args:
            vip_file (str): مسار ملف بيانات أعضاء VIP
        """
        self.vip_file = vip_file
        self.vip_data: Dict = self._load_vip_data()

    def _load_vip_data(self) -> Dict:
        """تحميل بيانات المستخدمين VIP من الملف"""
        try:
            if os.path.exists(self.vip_file):
                with open(self.vip_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            # إنشاء ملف فارغ إذا لم يكن موجودًا
            with open(self.vip_file, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات VIP: {e}")
            return {}

    def _save_vip_data(self) -> bool:
        """حفظ بيانات المستخدمين VIP إلى الملف"""
        try:
            with open(self.vip_file, "w", encoding="utf-8") as f:
                json.dump(self.vip_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات VIP: {e}")
            return False

    def is_vip(self, user_id: int, admin_ids: list = None) -> bool:
        """
        التحقق من حالة VIP للمستخدم
        
        Args:
            user_id (int): معرف المستخدم
            admin_ids (list): قائمة معرفات المشرفين (اختيارية)
            
        Returns:
            bool: True إذا كان المستخدم VIP أو مشرف
        """
        try:
            # المشرفون هم VIP تلقائيًا
            if admin_ids and user_id in admin_ids:
                return True

            user = self.vip_data.get(str(user_id))
            if not user:
                return False

            expiry = datetime.strptime(user["expires_at"], "%Y-%m-%d").date()
            return expiry >= datetime.now().date()
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة VIP للمستخدم {user_id}: {e}")
            return False

    def add_vip(self, user_id: int, name: str, days: int) -> bool:
        """
        إضافة مستخدم جديد كـ VIP
        
        Args:
            user_id (int): معرف المستخدم
            name (str): اسم المستخدم
            days (int): عدد أيام الاشتراك
            
        Returns:
            bool: True إذا تمت الإضافة بنجاح
        """
        try:
            joined_at = datetime.now().date()
            expires_at = joined_at + timedelta(days=days)

            self.vip_data[str(user_id)] = {
                "name": name,
                "joined_at": str(joined_at),
                "expires_at": str(expires_at),
                "days_total": days
            }

            return self._save_vip_data()
        except Exception as e:
            logger.error(f"خطأ في إضافة مستخدم VIP {user_id}: {e}")
            return False

    def remove_vip(self, user_id: int) -> bool:
        """
        حذف مستخدم VIP
        
        Args:
            user_id (int): معرف المستخدم
            
        Returns:
            bool: True إذا تم الحذف بنجاح
        """
        try:
            if str(user_id) in self.vip_data:
                del self.vip_data[str(user_id)]
                return self._save_vip_data()
            return False
        except Exception as e:
            logger.error(f"خطأ في حذف مستخدم VIP {user_id}: {e}")
            return False

    def get_vip_info(self, user_id: int) -> Optional[Dict]:
        """
        الحصول على معلومات VIP للمستخدم
        
        Args:
            user_id (int): معرف المستخدم
            
        Returns:
            Optional[Dict]: معلومات المستخدم أو None
        """
        return self.vip_data.get(str(user_id))

    def get_all_vip_users(self) -> Dict:
        """
        الحصول على جميع مستخدمي VIP
        
        Returns:
            Dict: قاموس جميع أعضاء VIP
        """
        return self.vip_data

    def extend_vip(self, user_id: int, additional_days: int) -> bool:
        """
        تمديد اشتراك VIP
        
        Args:
            user_id (int): معرف المستخدم
            additional_days (int): عدد الأيام الإضافية
            
        Returns:
            bool: True إذا تم التمديد بنجاح
        """
        try:
            vip_info = self.get_vip_info(user_id)
            if not vip_info:
                return False

            current_expiry = vip_info.get("expires_at", "")
            
            # حساب تاريخ الانتهاء الجديد
            try:
                current_expiry_date = datetime.strptime(current_expiry, "%Y-%m-%d").date()
                # إذا كان الاشتراك منتهي، نبدأ من اليوم الحالي
                if current_expiry_date < datetime.now().date():
                    new_expiry_date = datetime.now().date() + timedelta(days=additional_days)
                else:
                    new_expiry_date = current_expiry_date + timedelta(days=additional_days)
            except:
                # في حالة خطأ في التاريخ، نبدأ من اليوم الحالي
                new_expiry_date = datetime.now().date() + timedelta(days=additional_days)

            # تحديث بيانات العضو
            vip_info["expires_at"] = str(new_expiry_date)
            vip_info["days_total"] = vip_info.get("days_total", 0) + additional_days

            # حفظ البيانات المحدثة
            self.vip_data[str(user_id)] = vip_info
            return self._save_vip_data()
            
        except Exception as e:
            logger.error(f"خطأ في تمديد اشتراك VIP للمستخدم {user_id}: {e}")
            return False

    def clean_expired_vip(self) -> tuple[int, list]:
        """
        تنظيف أعضاء VIP المنتهية الصلاحية
        
        Returns:
            tuple[int, list]: عدد الأعضاء المحذوفين وقائمة بأسمائهم
        """
        try:
            vip_users = self.get_all_vip_users()
            
            if not vip_users:
                return 0, []

            expired_users = []
            current_date = datetime.now().date()

            # التحقق من كل عضو VIP
            for user_id, user_info in vip_users.items():
                try:
                    expiry_date = user_info.get("expires_at", "")
                    if expiry_date:
                        expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                        if expiry < current_date:
                            expired_users.append((user_id, user_info.get("name", "غير معروف")))
                except Exception as e:
                    logger.error(f"خطأ في التحقق من عضو VIP {user_id}: {e}")

            # حذف الأعضاء المنتهية الصلاحية
            removed_count = 0
            removed_names = []

            for user_id, name in expired_users:
                if self.remove_vip(int(user_id)):
                    removed_names.append(f"• {name} (ID: {user_id})")
                    removed_count += 1
                else:
                    logger.error(f"فشل في حذف عضو VIP: {name} (ID: {user_id})")

            return removed_count, removed_names
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف أعضاء VIP المنتهية الصلاحية: {e}")
            return 0, []

    def get_vip_statistics(self) -> Dict:
        """
        الحصول على إحصائيات أعضاء VIP
        
        Returns:
            Dict: إحصائيات مفصلة
        """
        try:
            vip_users = self.get_all_vip_users()
            total_users = len(vip_users)
            
            if total_users == 0:
                return {
                    "total": 0,
                    "active": 0,
                    "expired": 0,
                    "expiring_soon": 0
                }

            active_users = 0
            expired_users = 0
            expiring_soon = 0
            current_date = datetime.now().date()
            warning_date = current_date + timedelta(days=7)  # تحذير قبل أسبوع

            for user_info in vip_users.values():
                try:
                    expiry_date = user_info.get("expires_at", "")
                    if expiry_date:
                        expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                        if expiry >= current_date:
                            active_users += 1
                            if expiry <= warning_date:
                                expiring_soon += 1
                        else:
                            expired_users += 1
                except:
                    expired_users += 1

            return {
                "total": total_users,
                "active": active_users,
                "expired": expired_users,
                "expiring_soon": expiring_soon
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات VIP: {e}")
            return {"total": 0, "active": 0, "expired": 0, "expiring_soon": 0}


class VIPAuthorizationManager:
    """مدير التحقق من الصلاحيات والتفويض"""

    def __init__(self, vip_manager: VIPManager, admin_ids: list, vip_contact_url: str = "https://t.me/GurusVIP"):
        """
        تهيئة مدير التفويض

        Args:
            vip_manager (VIPManager): مدير VIP
            admin_ids (list): قائمة معرفات المشرفين
            vip_contact_url (str): رابط التواصل للاشتراك في VIP
        """
        self.vip_manager = vip_manager
        self.admin_ids = admin_ids
        self.vip_contact_url = vip_contact_url

    def is_admin(self, user_id: int) -> bool:
        """
        التحقق مما إذا كان المستخدم مشرفًا

        Args:
            user_id (int): معرف المستخدم

        Returns:
            bool: True إذا كان مشرفًا
        """
        return user_id in self.admin_ids

    def is_authorized_user(self, user_id: int) -> bool:
        """
        التحقق من كون المستخدم مخول لاستخدام البوت (مشرف أو VIP)

        Args:
            user_id (int): معرف المستخدم

        Returns:
            bool: True إذا كان مخولاً
        """
        return self.is_admin(user_id) or self.vip_manager.is_vip(user_id, self.admin_ids)

    async def check_user_authorization(self, update: Update, channel_username: str = None) -> bool:
        """
        التحقق من صلاحية المستخدم وإرسال رسالة إذا لم يكن مخولاً

        Args:
            update (Update): تحديث تيليجرام
            channel_username (str): اسم المستخدم للقناة (اختياري)

        Returns:
            bool: True إذا كان المستخدم مخولاً
        """
        user_id = update.effective_user.id

        if not self.is_authorized_user(user_id):
            unauthorized_message = (
                "╔══════ <b>🌟 نظام العضوية المميزة</b> 🌟 ══════╗\n\n"
                "🔒 <b>مرحباً بك في بوت سحب المنشورات المتطور!</b>\n\n"
                "💎 <b>عرض خاص: اشترك في VIP مقابل 1$ شهرياً فقط!</b>\n\n"

                "<b>✨ المزايا الحصرية للأعضاء المميزين:</b>\n"
                "◈ 🚀 سحب المنشورات بسرعة فائقة\n"
                "◈ 💫 روابط مباشرة بدون إعلانات أو اختصارات\n"
                "◈ ⚡ تجاوز صفحات الانتظار والإعلانات\n"
                "◈ 🛡️ دعم فني متميز على مدار الساعة\n"
                "◈ 🎯 أولوية في المعالجة والسحب\n"
                "◈ 📊 إحصائيات مفصلة لعمليات السحب\n"
                "◈ 🔥 ميزات حصرية للأعضاء المميزين\n\n"

                "<b>💰 العرض الحصري:</b>\n"
                "◈ 📌 سعر الاشتراك: 1$ شهرياً فقط\n"
                "◈ 🎁 تجربة مجانية للمشتركين الجدد\n"
                "◈ 💸 وفر وقتك وجهدك في سحب المنشورات\n\n"

                "<b>📢 خيار مجاني متاح!</b>\n"
                "◈ 📱 تابع قناتنا المجانية للمحتوى\n"
                "◈ 🔄 روابط مختصرة لدعم خدماتنا\n"
                "◈ 📚 محتوى منظم ومفيد\n\n"

                "<b>⭐️ احصل على تجربة VIP الآن!</b>\n"
                "◈ 🔓 روابط مباشرة بدون إعلانات\n"
                "◈ 🚀 سحب سريع وموثوق للمنشورات\n"
                "◈ 💫 ميزات حصرية إضافية\n\n"

                "<b>💳 وسائل الدفع المتوفرة:</b>\n"
                "◈ 🟡 <b>Binance Pay</b> – نقبل USDT / BNB / BTC وغيرها\n"
                "◈ 🌐 <b>WebMoney</b> – الدفع عبر WMZ\n"
                "◈ 💸 <b>Payeer</b> – الدفع بالدولار أو الروبل\n"
                "◈ 📲 <b>Telegram Wallet</b> – الدفع عبر المحفظة مباشرة"
            )

            keyboard = [
                [InlineKeyboardButton("💎 اشترك الآن", url=self.vip_contact_url)]
            ]

            if channel_username:
                keyboard.append([InlineKeyboardButton("📢 قناة الدورات", url=f"https://t.me/{channel_username}")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                unauthorized_message,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
            return False

        return True

    async def admin_only_check(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        التحقق من صلاحيات المشرف وإرسال رسالة خطأ إذا لم يكن مشرفًا

        Args:
            update (Update): تحديث تيليجرام
            context (ContextTypes.DEFAULT_TYPE): سياق البوت

        Returns:
            bool: True إذا كان مشرفًا
        """
        if not self.is_admin(update.effective_user.id):
            await update.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
            return False
        return True


class VIPCommandHandlers:
    """معالجات أوامر VIP للتيليجرام"""

    def __init__(self, vip_manager: VIPManager, auth_manager: VIPAuthorizationManager):
        """
        تهيئة معالجات الأوامر

        Args:
            vip_manager (VIPManager): مدير VIP
            auth_manager (VIPAuthorizationManager): مدير التفويض
        """
        self.vip_manager = vip_manager
        self.auth_manager = auth_manager

    async def handle_vip_subscribe(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """معالجة طلب الاشتراك في خدمة VIP"""
        query = update.callback_query
        user_id = update.effective_user.id

        # التحقق مما إذا كان المستخدم VIP بالفعل
        if self.vip_manager.is_vip(user_id, self.auth_manager.admin_ids):
            await query.message.reply_text(
                "✅ أنت بالفعل مشترك في خدمة VIP!\n"
                "يمكنك الاستمتاع بالروابط المباشرة بدون اختصار.\n"
                "استخدم الأمر /vip_info لعرض تفاصيل اشتراكك."
            )
            return

        message = (
            "💎 <b>اشترك في خدمة VIP الآن!</b> 💎\n\n"
            "🎓 <b>استفد من أقوى عروض الكورسات التعليمية!</b>\n\n"
            "✅ روابط مباشرة بدون إعلانات أو اختصارات\n"
            "✅ وصول فوري لأحدث الكورسات\n"
            "✅ دعم فني مميز وسريع\n"
            "✅ وفر وقتك وجهدك في البحث!\n\n"
            "💸 <b>العرض الحالي:</b>\n"
            "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
            "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
            "📲 للاشتراك، تواصل مع المسؤول:\n"
            f"👤 {self.auth_manager.vip_contact_url.replace('https://t.me/', '@')}"
        )

        # إنشاء أزرار
        keyboard = [
            [InlineKeyboardButton("💬 تواصل مع المسؤول", url=self.auth_manager.vip_contact_url)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.message.reply_text(
            message,
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

    async def handle_vip_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """معالجة طلب عرض معلومات VIP"""
        # يمكن استدعاؤها من callback_query أو command
        if hasattr(update, 'callback_query') and update.callback_query:
            query = update.callback_query
            user_id = update.effective_user.id
            user_name = update.effective_user.first_name
            reply_method = query.message.reply_text
        else:
            user_id = update.effective_user.id
            user_name = update.effective_user.first_name
            reply_method = update.message.reply_text

        # التحقق مما إذا كان المستخدم VIP
        if self.vip_manager.is_vip(user_id, self.auth_manager.admin_ids):
            # الحصول على معلومات VIP
            vip_info = self.vip_manager.get_vip_info(user_id)

            if vip_info:
                joined_date = vip_info.get("joined_at", "غير محدد")
                expiry_date = vip_info.get("expires_at", "غير محدد")
                days_total = vip_info.get("days_total", 0)

                # حساب الأيام المتبقية
                try:
                    expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                    days_left = (expiry - datetime.now().date()).days
                    days_left_text = f"{days_left} يوم" if days_left > 0 else "منتهي"
                except:
                    days_left_text = "غير محدد"

                message = (
                    "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                    f"👤 المستخدم: {user_name}\n"
                    f"📅 تاريخ الاشتراك: {joined_date}\n"
                    f"⏳ تاريخ الانتهاء: {expiry_date}\n"
                    f"⌛ المدة المتبقية: {days_left_text}\n"
                    f"📊 مدة الاشتراك: {days_total} يوم\n\n"
                    "✅ أنت تتمتع بجميع مميزات VIP!\n"
                    "• روابط مباشرة بدون اختصار\n"
                    "• تجاوز صفحات الإعلانات\n"
                    "• دعم فني مميز"
                )
            else:
                # في حالة كان المستخدم مشرفًا (VIP تلقائي)
                message = (
                    "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                    f"👤 المستخدم: {user_name}\n"
                    "🔑 الحالة: مشرف (VIP تلقائي)\n\n"
                    "✅ أنت تتمتع بجميع مميزات VIP!\n"
                    "• روابط مباشرة بدون اختصار\n"
                    "• تجاوز صفحات الإعلانات\n"
                    "• دعم فني مميز"
                )
        else:
            # رسالة للمستخدمين غير VIP
            message = (
                "💎 <b>معلومات اشتراك VIP</b> 💎\n\n"
                "❌ أنت غير مشترك في خدمة VIP.\n\n"
                "🎓 <b>استفد من أقوى عروض الكورسات التعليمية الآن!</b>\n\n"
                "✅ روابط مباشرة بدون إعلانات أو اختصارات\n"
                "✅ وصول فوري لأحدث الكورسات\n"
                "✅ دعم فني مميز وسريع\n"
                "✅ وفر وقتك وجهدك في البحث!\n\n"
                "💸 <b>العرض الحالي:</b>\n"
                "📆 اشتراك لمدة شهر واحد فقط بـ 1 دولار!\n"
                "🎯 استثمر في نفسك بأقل من ثمن كوب شاي يوميًا\n"
                "⚠️ العرض محدود — سارع بالاشتراك الآن!\n\n"
                "📩 للاشتراك والتواصل:\n"
                f"👤 {self.auth_manager.vip_contact_url.replace('https://t.me/', '@')}"
            )

        # إنشاء أزرار
        keyboard = [
            [InlineKeyboardButton("💬 تواصل مع المسؤول", url=self.auth_manager.vip_contact_url)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await reply_method(
            message,
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

    async def add_vip_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """إضافة مستخدم VIP (للمشرفين فقط)"""
        # التحقق من صلاحيات المشرف
        if not await self.auth_manager.admin_only_check(update, context):
            return

        # التحقق من وجود المعلمات المطلوبة
        if not context.args or len(context.args) < 2:
            await update.message.reply_text(
                "❌ يرجى تحديد معرف المستخدم وعدد الأيام\n\n"
                "الاستخدام الصحيح:\n"
                "/add_vip 123456789 30 اسم_المستخدم"
            )
            return

        try:
            # استخراج المعلمات
            user_id = int(context.args[0])
            days = int(context.args[1])
            name = " ".join(context.args[2:]) if len(context.args) > 2 else f"VIP User {user_id}"

            # إضافة المستخدم
            if self.vip_manager.add_vip(user_id, name, days):
                expiry_date = (datetime.now() + timedelta(days=days)).strftime("%Y-%m-%d")
                await update.message.reply_text(
                    f"✅ تم إضافة عضو VIP جديد بنجاح!\n\n"
                    f"👤 الاسم: {name}\n"
                    f"🆔 المعرف: {user_id}\n"
                    f"⏳ المدة: {days} يوم\n"
                    f"📅 تاريخ الانتهاء: {expiry_date}"
                )
            else:
                await update.message.reply_text("❌ فشل في إضافة العضو")
        except ValueError:
            await update.message.reply_text("❌ تأكد من صحة المعرف وعدد الأيام")
        except Exception as e:
            await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

    async def remove_vip_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """حذف مستخدم VIP (للمشرفين فقط)"""
        # التحقق من صلاحيات المشرف
        if not await self.auth_manager.admin_only_check(update, context):
            return

        # التحقق من وجود المعلمات المطلوبة
        if not context.args or len(context.args) < 1:
            await update.message.reply_text(
                "❌ يرجى تحديد معرف المستخدم المراد حذفه\n\n"
                "الاستخدام الصحيح:\n"
                "/remove_vip 123456789"
            )
            return

        try:
            # استخراج المعلمات
            user_id = int(context.args[0])

            # الحصول على معلومات المستخدم قبل الحذف
            vip_info = self.vip_manager.get_vip_info(user_id)

            if not vip_info:
                await update.message.reply_text(
                    f"❌ المستخدم غير موجود في قائمة VIP\n"
                    f"المعرف: {user_id}"
                )
                return

            user_name = vip_info.get("name", "غير معروف")

            # حذف المستخدم
            if self.vip_manager.remove_vip(user_id):
                await update.message.reply_text(
                    f"✅ تم حذف عضو VIP بنجاح!\n\n"
                    f"👤 الاسم: {user_name}\n"
                    f"🆔 المعرف: {user_id}\n"
                    f"🗑️ تم الحذف في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                await update.message.reply_text("❌ فشل في حذف العضو")
        except ValueError:
            await update.message.reply_text("❌ تأكد من صحة معرف المستخدم")
        except Exception as e:
            await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

    async def extend_vip_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """تمديد اشتراك VIP (للمشرفين فقط)"""
        # التحقق من صلاحيات المشرف
        if not await self.auth_manager.admin_only_check(update, context):
            return

        # التحقق من وجود المعلمات المطلوبة
        if not context.args or len(context.args) < 2:
            await update.message.reply_text(
                "❌ يرجى تحديد معرف المستخدم وعدد الأيام للتمديد\n\n"
                "الاستخدام الصحيح:\n"
                "/extend_vip 123456789 30"
            )
            return

        try:
            # استخراج المعلمات
            user_id = int(context.args[0])
            additional_days = int(context.args[1])

            if additional_days <= 0:
                await update.message.reply_text("❌ عدد الأيام يجب أن يكون أكبر من صفر")
                return

            # الحصول على معلومات المستخدم الحالية
            vip_info = self.vip_manager.get_vip_info(user_id)

            if not vip_info:
                await update.message.reply_text(
                    f"❌ المستخدم غير موجود في قائمة VIP\n"
                    f"المعرف: {user_id}\n\n"
                    f"استخدم /add_vip لإضافة عضو جديد"
                )
                return

            user_name = vip_info.get("name", "غير معروف")
            current_expiry = vip_info.get("expires_at", "")

            # تمديد الاشتراك
            if self.vip_manager.extend_vip(user_id, additional_days):
                # الحصول على التاريخ الجديد
                updated_info = self.vip_manager.get_vip_info(user_id)
                new_expiry = updated_info.get("expires_at", "")

                await update.message.reply_text(
                    f"✅ تم تمديد اشتراك VIP بنجاح!\n\n"
                    f"👤 الاسم: {user_name}\n"
                    f"🆔 المعرف: {user_id}\n"
                    f"📅 التاريخ السابق: {current_expiry}\n"
                    f"📅 التاريخ الجديد: {new_expiry}\n"
                    f"➕ الأيام المضافة: {additional_days} يوم"
                )
            else:
                await update.message.reply_text("❌ فشل في تمديد الاشتراك")

        except ValueError:
            await update.message.reply_text("❌ تأكد من صحة المعرف وعدد الأيام")
        except Exception as e:
            await update.message.reply_text(f"❌ حدث خطأ غير متوقع: {str(e)}")

    async def show_vip_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """عرض واجهة إدارة أعضاء VIP بتنسيق جدول احترافي"""
        if not await self.auth_manager.admin_only_check(update, context):
            return

        # قراءة قائمة أعضاء VIP
        vip_users = self.vip_manager.get_all_vip_users()

        # إنشاء رسالة تعرض قائمة الأعضاء بتنسيق جدول
        message_text = "👥 <b>لوحة إدارة أعضاء VIP</b>\n\n"

        if vip_users:
            # إنشاء رأس الجدول
            message_text += "<pre>┌─────────────────────────────────────────────────────┐\n"
            message_text += "│ المعرف       │ الاسم           │ الحالة  │ تاريخ الانتهاء   │\n"
            message_text += "├─────────────────────────────────────────────────────┤\n"

            # إضافة بيانات الأعضاء
            vip_list = list(vip_users.items())
            for user_id, user_info in vip_list:
                name = user_info.get("name", "غير معروف")
                # اقتصار الاسم على 15 حرفًا كحد أقصى
                if len(name) > 15:
                    name = name[:12] + "..."

                expiry_date = user_info.get("expires_at", "غير محدد")

                # تحديد حالة العضوية
                try:
                    expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                    current_date = datetime.now().date()

                    if expiry >= current_date:
                        days_left = (expiry - current_date).days
                        if days_left > 7:
                            status = "نشط ✅"
                        elif days_left > 0:
                            status = f"ينتهي قريباً ⚠️"
                        else:
                            status = "ينتهي اليوم ⏰"
                    else:
                        status = "منتهي ❌"
                except:
                    status = "خطأ ⚠️"

                # تنسيق الصف
                user_id_str = str(user_id)[:10].ljust(10)
                name_str = name.ljust(15)
                status_str = status.ljust(8)
                expiry_str = expiry_date.ljust(12)

                message_text += f"│ {user_id_str} │ {name_str} │ {status_str} │ {expiry_str} │\n"

            # إغلاق الجدول
            message_text += "└─────────────────────────────────────────────────────┘</pre>\n\n"

            # إضافة إحصائيات
            stats = self.vip_manager.get_vip_statistics()
            message_text += f"<b>📊 الإحصائيات:</b>\n"
            message_text += f"• إجمالي الأعضاء: {stats['total']}\n"
            message_text += f"• الأعضاء النشطين: {stats['active']}\n"
            message_text += f"• الاشتراكات المنتهية: {stats['expired']}\n"
            message_text += f"• ينتهي قريباً: {stats['expiring_soon']}\n\n"
        else:
            message_text += "<i>لا يوجد أعضاء VIP حالياً.</i>\n\n"

        # إضافة أزرار للإدارة
        message_text += "<b>🛠️ إدارة الأعضاء:</b>\n\n"
        message_text += "• <b>إضافة عضو جديد:</b>\n"
        message_text += "<code>/add_vip معرف_المستخدم عدد_الأيام الاسم</code>\n"
        message_text += "<b>مثال:</b> <code>/add_vip 123456789 30 محمد أحمد</code>\n\n"
        message_text += "• <b>حذف عضو:</b>\n"
        message_text += "<code>/remove_vip معرف_المستخدم</code>\n"
        message_text += "<b>مثال:</b> <code>/remove_vip 123456789</code>\n\n"
        message_text += "• <b>تمديد اشتراك:</b>\n"
        message_text += "<code>/extend_vip معرف_المستخدم عدد_الأيام</code>\n"
        message_text += "<b>مثال:</b> <code>/extend_vip 123456789 30</code>"

        # إنشاء أزرار لإدارة الأعضاء
        keyboard = []

        # إضافة زر حذف أمام كل عضو VIP (أول 10 أعضاء فقط لتجنب تجاوز حد الأزرار)
        if vip_users:
            vip_list = list(vip_users.items())[:10]  # أول 10 أعضاء فقط

            for user_id, user_info in vip_list:
                name = user_info.get("name", "غير معروف")
                if len(name) > 15:
                    name = name[:12] + "..."

                # تحديد حالة العضوية للعرض في الزر
                expiry_date = user_info.get("expires_at", "")
                try:
                    expiry = datetime.strptime(expiry_date, "%Y-%m-%d").date()
                    days_left = (expiry - datetime.now().date()).days
                    if days_left > 0:
                        status_emoji = "✅"
                        days_text = f"({days_left}د)"
                    else:
                        status_emoji = "❌"
                        days_text = "(منتهي)"
                except:
                    status_emoji = "⚠️"
                    days_text = "(خطأ)"

                keyboard.append([
                    InlineKeyboardButton(
                        f"{status_emoji} {name} {days_text}",
                        callback_data='vip_info_display'
                    ),
                    InlineKeyboardButton(
                        "🗑️ حذف",
                        callback_data=f'remove_vip_{user_id}'
                    )
                ])

            # إضافة تحذير إذا كان هناك أكثر من 10 أعضاء
            if len(vip_users) > 10:
                keyboard.append([InlineKeyboardButton("⚠️ يتم عرض أول 10 أعضاء فقط", callback_data='vip_info_display')])

        # أزرار التحكم الرئيسية
        keyboard.extend([
            [InlineKeyboardButton("🔄 تحديث القائمة", callback_data='manage_vip')],
            [InlineKeyboardButton("🧹 تنظيف المنتهية", callback_data='clean_expired_vip')],
            [InlineKeyboardButton("🔙 العودة", callback_data='admin_panel')]
        ])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # إرسال الرسالة أو تحديثها
        if hasattr(update, 'callback_query') and update.callback_query:
            await update.callback_query.message.edit_text(
                message_text,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        else:
            await update.message.reply_text(
                message_text,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )

    async def clean_expired_vip_manual(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """تنظيف أعضاء VIP المنتهية الصلاحية يدوياً"""
        # التحقق من صلاحيات المشرف
        if not await self.auth_manager.admin_only_check(update, context):
            return

        # إرسال رسالة التحميل
        if hasattr(update, 'callback_query') and update.callback_query:
            message = await update.callback_query.message.reply_text("🧹 جاري تنظيف أعضاء VIP المنتهية الصلاحية...")
        else:
            message = await update.message.reply_text("🧹 جاري تنظيف أعضاء VIP المنتهية الصلاحية...")

        try:
            # تنظيف الأعضاء المنتهية الصلاحية
            removed_count, removed_names = self.vip_manager.clean_expired_vip()

            if removed_count > 0:
                result_message = f"✅ تم حذف {removed_count} عضو VIP منتهي الصلاحية:\n\n"
                result_message += "\n".join(removed_names[:10])  # عرض أول 10 أعضاء فقط
                if len(removed_names) > 10:
                    result_message += f"\n... و {len(removed_names) - 10} عضو آخر"
            else:
                result_message = "✅ لا يوجد أعضاء VIP منتهية الصلاحية للحذف"

            await message.edit_text(result_message)

        except Exception as e:
            logger.error(f"خطأ في تنظيف أعضاء VIP المنتهية الصلاحية: {e}")
            await message.edit_text("❌ حدث خطأ أثناء تنظيف أعضاء VIP")

    async def handle_remove_vip_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """معالجة زر حذف عضو VIP"""
        query = update.callback_query

        # التحقق من صلاحيات المشرف
        if not self.auth_manager.is_admin(update.effective_user.id):
            await query.message.reply_text("⚠️ هذه الميزة متاحة للمشرفين فقط")
            return

        # استخراج معرف المستخدم من البيانات
        user_id_to_remove = query.data.replace('remove_vip_', '')

        try:
            user_id_to_remove = int(user_id_to_remove)

            # الحصول على معلومات المستخدم قبل الحذف
            vip_info = self.vip_manager.get_vip_info(user_id_to_remove)
            user_name = vip_info.get("name", "غير معروف") if vip_info else "غير معروف"

            # حذف المستخدم
            if self.vip_manager.remove_vip(user_id_to_remove):
                await query.message.reply_text(
                    f"✅ تم حذف العضو بنجاح!\n\n"
                    f"👤 الاسم: {user_name}\n"
                    f"🆔 المعرف: {user_id_to_remove}\n"
                    f"🗑️ تم الحذف في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                # تحديث قائمة إدارة VIP تلقائياً
                await self.show_vip_management(update, context)
            else:
                await query.message.reply_text(
                    f"❌ فشل في حذف العضو!\n"
                    f"المعرف: {user_id_to_remove}\n"
                    f"قد يكون العضو غير موجود أو حدث خطأ في النظام."
                )

        except ValueError:
            await query.message.reply_text("❌ معرف المستخدم غير صالح")
        except Exception as e:
            logger.error(f"خطأ في حذف عضو VIP {user_id_to_remove}: {e}")
            await query.message.reply_text("❌ حدث خطأ غير متوقع أثناء حذف العضو")


def clean_expired_vip_job(vip_manager: VIPManager):
    """وظيفة تنظيف أعضاء VIP المنتهية الصلاحية (للجدولة التلقائية)"""
    logger.info("بدء تنظيف أعضاء VIP المنتهية الصلاحية...")
    try:
        removed_count, removed_names = vip_manager.clean_expired_vip()

        if removed_count > 0:
            logger.info(f"تم حذف {removed_count} عضو VIP منتهي الصلاحية")
            for name in removed_names:
                logger.info(f"تم حذف: {name}")
        else:
            logger.info("لا يوجد أعضاء VIP منتهية الصلاحية للحذف")

    except Exception as e:
        logger.error(f"خطأ في وظيفة تنظيف أعضاء VIP: {e}")


# مثال على الاستخدام
if __name__ == "__main__":
    # إعداد المتغيرات
    ADMIN_IDS = [1789531376]  # قائمة معرفات المشرفين
    VIP_CONTACT_URL = "https://t.me/GurusVIP"

    # إنشاء المدراء
    vip_manager = VIPManager("vip_users.json")
    auth_manager = VIPAuthorizationManager(vip_manager, ADMIN_IDS, VIP_CONTACT_URL)
    command_handlers = VIPCommandHandlers(vip_manager, auth_manager)

    # مثال على الاستخدام
    print("نظام العضوية المميزة جاهز للاستخدام!")
    print(f"إجمالي أعضاء VIP: {len(vip_manager.get_all_vip_users())}")

    # إحصائيات
    stats = vip_manager.get_vip_statistics()
    print(f"الأعضاء النشطين: {stats['active']}")
    print(f"الأعضاء المنتهية الصلاحية: {stats['expired']}")

    # مثال على إضافة عضو VIP
    # vip_manager.add_vip(123456789, "مستخدم تجريبي", 30)

    # مثال على التحقق من العضوية
    # is_vip = vip_manager.is_vip(123456789, ADMIN_IDS)
    # print(f"هل المستخدم VIP؟ {is_vip}")


"""
طريقة الاستخدام في مشروع تيليجرام بوت:

1. استيراد الكلاسات:
from vip_membership_system import VIPManager, VIPAuthorizationManager, VIPCommandHandlers

2. إعداد النظام:
ADMIN_IDS = [123456789]  # معرفات المشرفين
VIP_CONTACT_URL = "https://t.me/your_contact"

vip_manager = VIPManager("vip_users.json")
auth_manager = VIPAuthorizationManager(vip_manager, ADMIN_IDS, VIP_CONTACT_URL)
command_handlers = VIPCommandHandlers(vip_manager, auth_manager)

3. إضافة معالجات الأوامر:
application.add_handler(CommandHandler("vip_info", command_handlers.handle_vip_info))
application.add_handler(CommandHandler("add_vip", command_handlers.add_vip_command))
application.add_handler(CommandHandler("remove_vip", command_handlers.remove_vip_command))
application.add_handler(CommandHandler("extend_vip", command_handlers.extend_vip_command))

4. إضافة معالجات الأزرار:
application.add_handler(CallbackQueryHandler(command_handlers.handle_vip_subscribe, pattern="vip_subscribe"))
application.add_handler(CallbackQueryHandler(command_handlers.handle_vip_info, pattern="vip_info"))

5. التحقق من الصلاحيات في الوظائف:
async def some_function(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not await auth_manager.check_user_authorization(update):
        return
    # باقي الكود...

6. للجدولة التلقائية (اختياري):
from apscheduler.schedulers.background import BackgroundScheduler

scheduler = BackgroundScheduler()
scheduler.add_job(
    func=lambda: clean_expired_vip_job(vip_manager),
    trigger='cron',
    hour=2,  # كل يوم في الساعة 2 صباحاً
    minute=0
)
scheduler.start()
"""
