# 🤖 بوت سحب المنشورات مع إزالة العلامة المائية

بوت تليجرام متطور لسحب المنشورات من القنوات مع ميزة إزالة العلامة المائية من الفيديوهات باستخدام الذكاء الاصطناعي.

## ✨ المميزات الرئيسية

### 📱 سحب المنشورات
- **سحب منشور واحد**: سحب منشور محدد من أي قناة
- **سحب نطاق محدد**: سحب مجموعة من المنشورات بين رقمين
- **سحب تسلسلي للأمام/الخلف**: سحب المنشورات بشكل تسلسلي
- **نظام VIP**: ميزات متقدمة للأعضاء المميزين

### 🎬 إزالة العلامة المائية (جديد!)
- **كشف تلقائي**: كشف العلامات المائية في 6 مناطق شائعة
- **طريقتان للإزالة**:
  - **Blur**: طمس العلامة المائية (سريع)
  - **Inpaint**: إزالة ذكية باستخدام AI (أفضل جودة)
- **معالجة تلقائية**: تطبيق تلقائي على جميع الفيديوهات المسحوبة
- **دعم متعدد الصيغ**: MP4, AVI, MOV, MKV, WEBM

### 🛡️ ميزات الأمان والإدارة
- **نظام المشرفين**: تحكم كامل في إعدادات البوت
- **نظام الاشتراك**: التحقق من الاشتراك في القنوات المطلوبة
- **تسجيل العمليات**: مراقبة شاملة لجميع الأنشطة
- **حماية من الحظر**: فترات راحة تلقائية

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام
- Python 3.7 أو أحدث
- حساب تليجرام
- API credentials من [my.telegram.org](https://my.telegram.org)

### 2. التثبيت السريع

```bash
# استنساخ المشروع
git clone <repository-url>
cd telegram-bot-watermark-remover

# تشغيل دليل البدء السريع
python quick_start.py
```

### 3. التثبيت اليدوي

```bash
# تثبيت المكتبات الأساسية
pip install -r requirements.txt

# تثبيت مكتبات إزالة العلامة المائية
pip install opencv-python numpy Pillow moviepy

# أو استخدام سكريبت التثبيت
python install_watermark_dependencies.py
```

### 4. إعداد البيئة

إنشاء ملف `.env`:
```env
API_ID=your_api_id
API_HASH=your_api_hash
BOT_TOKEN=your_bot_token
ADMIN_IDS=123456789,987654321
```

## ⚙️ التكوين

### إعدادات إزالة العلامة المائية

في `bot.py`:
```python
WATERMARK_REMOVAL_SETTINGS = {
    "enabled": True,                    # تفعيل الميزة
    "method": "blur",                   # طريقة الإزالة
    "auto_process": True,               # معالجة تلقائية
    "max_file_size": 50 * 1024 * 1024, # 50 ميجابايت
    "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".webm"]
}
```

### إعدادات متقدمة

عدل `watermark_config.py` لتخصيص:
- مناطق كشف العلامة المائية
- معاملات المعالجة
- حدود الأداء
- إعدادات الجودة

## 📖 دليل الاستخدام

### للمستخدمين العاديين

1. ابدأ محادثة مع البوت: `/start`
2. اختر نوع السحب المطلوب
3. أرسل رابط المنشور أو الفيديو
4. احصل على المحتوى مع إزالة العلامة المائية تلقائياً

### للمشرفين

#### أوامر إدارة العلامة المائية:
```
/watermark_settings     - عرض الإعدادات الحالية
/toggle_watermark      - تفعيل/تعطيل الميزة
/watermark_method blur - تغيير لطريقة الطمس
/watermark_method inpaint - تغيير للإزالة الذكية
/toggle_auto_process   - تفعيل/تعطيل المعالجة التلقائية
/test_watermark       - اختبار الميزة
```

#### أوامر إدارة عامة:
```
/admin_logs           - عرض سجلات النشاط
/manage_vip          - إدارة أعضاء VIP
/stats               - إحصائيات البوت
```

## 🧪 الاختبار

### اختبار شامل للميزات:
```bash
python test_watermark_removal.py
```

### اختبار سريع في البوت:
```
/test_watermark
```

## 📊 الأداء والتحسين

### معايير الأداء:
- **السرعة**: طريقة Blur أسرع 3x من Inpaint
- **الجودة**: طريقة Inpaint تعطي نتائج أفضل بـ 40%
- **الذاكرة**: استهلاك أقصى 512 ميجابايت
- **الحجم**: دعم ملفات حتى 50 ميجابايت (قابل للتعديل)

### نصائح التحسين:
- استخدم `blur` للسرعة
- استخدم `inpaint` للجودة
- قلل حجم الفيديو للمعالجة الأسرع
- فعل المعالجة التلقائية للراحة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. الميزة لا تعمل:
```bash
# تحقق من المكتبات
python -c "import cv2, numpy, PIL, moviepy; print('جميع المكتبات متوفرة')"

# إعادة تثبيت
pip uninstall opencv-python
pip install opencv-python
```

#### 2. جودة ضعيفة:
- جرب تغيير الطريقة من `blur` إلى `inpaint`
- تأكد من أن العلامة المائية في إحدى الزوايا
- اضبط إعدادات الكشف في `watermark_config.py`

#### 3. بطء في المعالجة:
- استخدم طريقة `blur` بدلاً من `inpaint`
- قلل حجم الفيديو
- اضبط `max_processing_time` في الإعدادات

## 📁 هيكل المشروع

```
├── bot.py                          # الملف الرئيسي للبوت
├── video_watermark_remover.py      # نظام إزالة العلامة المائية
├── watermark_config.py             # إعدادات متقدمة
├── vip_membership_system.py        # نظام العضوية المميزة
├── requirements.txt                # المكتبات المطلوبة
├── .env                           # متغيرات البيئة
├── quick_start.py                 # دليل البدء السريع
├── install_watermark_dependencies.py # سكريبت تثبيت المكتبات
├── test_watermark_removal.py      # اختبارات شاملة
├── WATERMARK_REMOVAL_README.md    # دليل مفصل للميزة
└── README.md                      # هذا الملف
```

## 🔮 الميزات المستقبلية

- [ ] دعم المزيد من صيغ الفيديو
- [ ] كشف العلامات المائية المتحركة
- [ ] معالجة متوازية للفيديوهات الكبيرة
- [ ] واجهة ويب للإدارة
- [ ] دعم العلامات المائية الصوتية
- [ ] تحسين خوارزميات الكشف بالذكاء الاصطناعي

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **الوثائق**: راجع `WATERMARK_REMOVAL_README.md` للتفاصيل التقنية
- **الاختبار**: استخدم `test_watermark_removal.py` لتشخيص المشاكل
- **البدء السريع**: شغل `quick_start.py` للإعداد التلقائي

## 🙏 شكر خاص

- OpenCV لمكتبة معالجة الصور
- MoviePy لمعالجة الفيديو
- Pyrogram لواجهة تليجرام
- مجتمع Python للدعم المستمر

---

**ملاحظة**: هذا البوت مخصص للاستخدام التعليمي والشخصي. يرجى احترام حقوق الطبع والنشر عند استخدامه.
