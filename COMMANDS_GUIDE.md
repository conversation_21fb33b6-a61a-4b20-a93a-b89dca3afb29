# 📋 دليل الأوامر والأزرار - بوت سحب المنشورات مع إزالة العلامة المائية

## 🎬 أوامر إدارة العلامة المائية (للمشرفين فقط)

### الأوامر النصية:

#### `/watermark_settings`
- **الوصف**: عرض وتعديل إعدادات إزالة العلامة المائية
- **الاستخدام**: `/watermark_settings`
- **المميزات**: 
  - عرض الحالة الحالية للميزة
  - أزرار تفاعلية للتحكم السريع
  - معلومات مفصلة عن الإعدادات

#### `/toggle_watermark`
- **الوصف**: تفعيل أو تعطيل ميزة إزالة العلامة المائية
- **الاستخدام**: `/toggle_watermark`
- **النتيجة**: تبديل حالة الميزة بين مفعل/معطل

#### `/watermark_method`
- **الوصف**: تغيير طريقة إزالة العلامة المائية
- **الاستخدام**: 
  - `/watermark_method blur` - للطمس (أسرع)
  - `/watermark_method inpaint` - للإزالة الذكية (أفضل جودة)

#### `/toggle_auto_process`
- **الوصف**: تفعيل أو تعطيل المعالجة التلقائية للفيديوهات
- **الاستخدام**: `/toggle_auto_process`
- **ملاحظة**: عند التعطيل، ستحتاج لمعالجة الفيديوهات يدوياً

#### `/test_watermark`
- **الوصف**: اختبار ميزة إزالة العلامة المائية
- **الاستخدام**: `/test_watermark`
- **المميزات**: 
  - عرض الإعدادات الحالية
  - تعليمات الاختبار
  - معلومات عن كيفية عمل الميزة

## 🎛️ الأزرار التفاعلية

### في القائمة الرئيسية:
- **🎬 إعدادات العلامة المائية** (للمشرفين فقط)
  - يظهر فقط إذا كانت الميزة متاحة
  - يعرض الحالة الحالية (مفعل/معطل)

### في لوحة التحكم الإدارية:
- **🎬 إعدادات العلامة المائية: [الحالة]**
  - الوصول المباشر لإعدادات العلامة المائية
  - يعرض الحالة الحالية في النص

### في قائمة إعدادات العلامة المائية:

#### **🟢 تفعيل الميزة / 🔴 تعطيل الميزة**
- تبديل حالة الميزة
- يتغير النص حسب الحالة الحالية

#### **🔄 طريقة: [الطريقة الحالية] ↔️**
- تبديل بين طريقتي الإزالة
- `blur` ↔️ `inpaint`

#### **🟢 تفعيل المعالجة التلقائية / 🔴 تعطيل المعالجة التلقائية**
- تبديل المعالجة التلقائية للفيديوهات
- يتغير النص حسب الحالة الحالية

#### **🧪 اختبار الميزة**
- عرض معلومات الاختبار
- تعليمات لاختبار الميزة

#### **🔙 لوحة التحكم**
- العودة للوحة التحكم الإدارية

#### **🏠 القائمة الرئيسية**
- العودة للقائمة الرئيسية

## 📊 معلومات الحالة

### في الإعدادات يتم عرض:
- **التفعيل**: 🟢 مفعل / 🔴 معطل
- **المعالجة التلقائية**: 🟢 مفعلة / 🔴 معطلة  
- **طريقة الإزالة**: blur أو inpaint
- **الحد الأقصى للحجم**: بالميجابايت
- **الصيغ المدعومة**: قائمة بصيغ الفيديو

### في لوحة التحكم يتم عرض:
- **الحالة**: 🟢 مفعلة / 🔴 معطلة
- **الطريقة**: blur أو inpaint
- **المعالجة التلقائية**: 🟢 مفعلة / 🔴 معطلة

## 🔧 طرق الوصول للإعدادات

### 1. من القائمة الرئيسية:
`/start` → **🎬 إعدادات العلامة المائية**

### 2. من لوحة التحكم:
`/start` → **🎛️ لوحة التحكم** → **🎬 إعدادات العلامة المائية**

### 3. الأمر المباشر:
`/watermark_settings`

### 4. من رسالة المساعدة:
`/help` → استخدام الأوامر المذكورة في القسم الإداري

## 💡 نصائح للاستخدام

### للمشرفين:
1. **استخدم الأزرار التفاعلية** للتحكم السريع
2. **اختبر الميزة** بعد أي تغيير في الإعدادات
3. **راقب الأداء** عند استخدام طريقة inpaint
4. **فعل المعالجة التلقائية** لراحة المستخدمين

### لاختيار الطريقة المناسبة:
- **blur**: للسرعة والاستقرار
- **inpaint**: للجودة الأفضل (أبطأ)

### للاختبار:
1. فعل الميزة
2. استخدم `/test_watermark`
3. أرسل فيديو يحتوي على علامة مائية
4. تحقق من النتيجة

## ⚠️ ملاحظات مهمة

### متطلبات الميزة:
- تثبيت المكتبات: `opencv-python numpy Pillow moviepy`
- صلاحيات المشرف للتحكم في الإعدادات

### حدود الميزة:
- الحد الأقصى للحجم: 50 ميجابايت (قابل للتعديل)
- الصيغ المدعومة: MP4, AVI, MOV, MKV, WEBM
- تعمل بشكل أفضل مع العلامات المائية في الزوايا

### في حالة عدم توفر الميزة:
- ستظهر رسالة تنبيه
- لن تظهر الأزرار المتعلقة بالعلامة المائية
- البوت سيعمل بشكل طبيعي بدون هذه الميزة

## 🚀 البدء السريع

1. **تأكد من تثبيت المكتبات**:
   ```bash
   pip install opencv-python numpy Pillow moviepy
   ```

2. **شغل البوت**:
   ```bash
   python bot.py
   ```

3. **افتح البوت في تليجرام واستخدم**:
   `/watermark_settings`

4. **فعل الميزة والمعالجة التلقائية**

5. **اختبر بإرسال فيديو يحتوي على علامة مائية**

---

**ملاحظة**: جميع أوامر إدارة العلامة المائية متاحة للمشرفين فقط (المعرفين في ADMIN_IDS).
