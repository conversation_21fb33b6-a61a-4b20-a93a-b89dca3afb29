# 🎬 ميزة إزالة العلامة المائية من الفيديوهات

## 📋 نظرة عامة

تم إضافة ميزة متقدمة لإزالة العلامات المائية من الفيديوهات المسحوبة من تليجرام. هذه الميزة تستخدم تقنيات الذكاء الاصطناعي ومعالجة الصور لإزالة أو طمس العلامات المائية بشكل تلقائي.

## ✨ المميزات

### 🔧 طرق الإزالة
- **Blur (طمس)**: طمس منطقة العلامة المائية - أسرع وأكثر استقراراً
- **Inpaint (إزالة ذكية)**: إزالة العلامة المائية وملء المنطقة بذكاء - أبطأ لكن أفضل جودة

### 📱 الصيغ المدعومة
- MP4
- AVI  
- MOV
- MKV
- WEBM

### 🎯 كشف تلقائي للعلامات المائية
- كشف العلامات المائية في الزوايا الأربع
- كشف العلامات في الوسط العلوي والسفلي
- تحليل ذكي للمناطق المشبوهة

## 🚀 التثبيت والإعداد

### 1. تثبيت المكتبات المطلوبة

```bash
pip install opencv-python numpy Pillow moviepy
```

### 2. التحقق من التثبيت

عند تشغيل البوت، ستظهر رسالة:
- ✅ إذا تم تحميل النظام بنجاح
- ⚠️ إذا فشل التحميل مع تعليمات الإصلاح

## 🎛️ الإعدادات والتحكم

### للمشرفين فقط:

#### أوامر التحكم:
- `/watermark_settings` - عرض الإعدادات الحالية
- `/toggle_watermark` - تفعيل/تعطيل الميزة
- `/watermark_method [blur/inpaint]` - تغيير طريقة الإزالة
- `/toggle_auto_process` - تفعيل/تعطيل المعالجة التلقائية
- `/test_watermark` - اختبار الميزة

#### واجهة الأزرار:
- زر "🎬 إعدادات العلامة المائية" في القائمة الرئيسية للمشرفين
- أزرار تفاعلية لتغيير الإعدادات

## 📊 الإعدادات الافتراضية

```python
WATERMARK_REMOVAL_SETTINGS = {
    "enabled": True,                    # تفعيل الميزة
    "method": "blur",                   # طريقة الإزالة
    "auto_process": True,               # معالجة تلقائية
    "max_file_size": 50 * 1024 * 1024, # 50 ميجابايت حد أقصى
    "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".webm"]
}
```

## 🔄 كيفية العمل

### 1. المعالجة التلقائية
عند سحب فيديو:
1. يتم تحميل الفيديو مؤقتاً
2. كشف العلامة المائية في المناطق الشائعة
3. معالجة الفيديو حسب الطريقة المحددة
4. إرسال الفيديو المعالج مع رسالة تأكيد
5. تنظيف الملفات المؤقتة

### 2. خوارزمية الكشف
- تحليل التباين في المناطق المشبوهة
- البحث في 6 مناطق شائعة للعلامات المائية
- اختيار المنطقة الأكثر احتمالاً لاحتواء علامة مائية

### 3. طرق المعالجة

#### Blur (الطمس):
- سريع ومستقر
- يطمس المنطقة بفلتر Gaussian
- مناسب للاستخدام العام

#### Inpaint (الإزالة الذكية):
- أبطأ لكن أفضل جودة
- يستخدم خوارزمية TELEA للملء الذكي
- مناسب للحصول على أفضل النتائج

## 🛡️ الأمان والحدود

### حدود الحجم:
- الحد الأقصى: 50 ميجابايت (قابل للتعديل)
- الفيديوهات الأكبر يتم تخطيها

### الأمان:
- معالجة آمنة للأخطاء
- تنظيف تلقائي للملفات المؤقتة
- عدم تخزين الفيديوهات بشكل دائم

## 📝 أمثلة الاستخدام

### للمستخدمين العاديين:
1. اختر "🆓 سحب منشور واحد"
2. أرسل رابط فيديو يحتوي على علامة مائية
3. احصل على الفيديو مع إزالة/طمس العلامة المائية تلقائياً

### للمشرفين:
```
/watermark_settings          # عرض الإعدادات
/toggle_watermark           # تفعيل/تعطيل
/watermark_method blur      # تغيير للطمس
/watermark_method inpaint   # تغيير للإزالة الذكية
/test_watermark            # اختبار الميزة
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. الميزة لا تعمل:
- تأكد من تثبيت جميع المكتبات المطلوبة
- تحقق من رسائل الخطأ في السجل

#### 2. جودة المعالجة ضعيفة:
- جرب تغيير الطريقة من blur إلى inpaint
- تأكد من أن العلامة المائية في إحدى المناطق المدعومة

#### 3. الفيديو لا يتم معالجته:
- تحقق من حجم الفيديو (أقل من 50 ميجابايت)
- تأكد من أن الصيغة مدعومة
- تحقق من تفعيل المعالجة التلقائية

## 📈 الأداء والتحسين

### نصائح للأداء الأفضل:
- استخدم طريقة "blur" للسرعة
- استخدم طريقة "inpaint" للجودة
- قم بتقليل حجم الفيديو إذا أمكن

### مراقبة الأداء:
- تتبع أوقات المعالجة في السجل
- مراقبة استخدام الذاكرة
- تنظيف دوري للملفات المؤقتة

## 🔮 التطوير المستقبلي

### ميزات مخططة:
- دعم المزيد من صيغ الفيديو
- تحسين خوارزمية الكشف
- معالجة متوازية للفيديوهات الكبيرة
- واجهة مستخدم محسنة للإعدادات

### تحسينات محتملة:
- استخدام الذكاء الاصطناعي للكشف
- دعم العلامات المائية المتحركة
- معالجة الصوت المصاحب

## 📞 الدعم

للحصول على المساعدة:
1. تحقق من السجلات للأخطاء
2. استخدم أمر `/test_watermark` للاختبار
3. راجع هذا الدليل للحلول الشائعة

---

**ملاحظة**: هذه الميزة تجريبية وقد تحتاج لتحسينات إضافية حسب نوع العلامات المائية المستخدمة.
