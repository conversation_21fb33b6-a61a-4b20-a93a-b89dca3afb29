#!/usr/bin/env python3
"""
اختبار نظام العضوية المميزة (VIP)
"""

import os
import sys
import json
from datetime import datetime, timedelta

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from vip_membership_system import VIPManager, VIPAuthorizationManager, VIPCommandHandlers
    print("✅ تم استيراد نظام VIP بنجاح!")
except ImportError as e:
    print(f"❌ فشل في استيراد نظام VIP: {e}")
    sys.exit(1)

def test_vip_system():
    """اختبار وظائف نظام VIP الأساسية"""
    
    print("\n🧪 بدء اختبار نظام VIP...")
    
    # إنشاء مدير VIP للاختبار
    test_vip_file = "test_vip_users.json"
    vip_manager = VIPManager(test_vip_file)
    
    # معرفات اختبارية
    admin_ids = [123456789]
    vip_contact_url = "https://t.me/GurusVIP"
    
    # إنشاء مدير التفويض
    auth_manager = VIPAuthorizationManager(vip_manager, admin_ids, vip_contact_url)
    
    print("✅ تم إنشاء مدراء النظام بنجاح")
    
    # اختبار إضافة عضو VIP
    test_user_id = 987654321
    test_user_name = "مستخدم تجريبي"
    test_days = 30
    
    print(f"\n📝 اختبار إضافة عضو VIP...")
    result = vip_manager.add_vip(test_user_id, test_user_name, test_days)
    if result:
        print(f"✅ تم إضافة العضو: {test_user_name} (ID: {test_user_id}) لمدة {test_days} يوم")
    else:
        print("❌ فشل في إضافة العضو")
        return False
    
    # اختبار التحقق من العضوية
    print(f"\n🔍 اختبار التحقق من العضوية...")
    is_vip = vip_manager.is_vip(test_user_id, admin_ids)
    if is_vip:
        print(f"✅ المستخدم {test_user_id} هو عضو VIP")
    else:
        print(f"❌ المستخدم {test_user_id} ليس عضو VIP")
        return False
    
    # اختبار الحصول على معلومات العضو
    print(f"\n📊 اختبار الحصول على معلومات العضو...")
    vip_info = vip_manager.get_vip_info(test_user_id)
    if vip_info:
        print(f"✅ معلومات العضو:")
        print(f"   - الاسم: {vip_info['name']}")
        print(f"   - تاريخ الانضمام: {vip_info['joined_at']}")
        print(f"   - تاريخ الانتهاء: {vip_info['expires_at']}")
        print(f"   - إجمالي الأيام: {vip_info['days_total']}")
    else:
        print("❌ فشل في الحصول على معلومات العضو")
        return False
    
    # اختبار تمديد العضوية
    print(f"\n⏰ اختبار تمديد العضوية...")
    extend_days = 15
    result = vip_manager.extend_vip(test_user_id, extend_days)
    if result:
        print(f"✅ تم تمديد العضوية بـ {extend_days} يوم إضافي")
        
        # التحقق من التمديد
        updated_info = vip_manager.get_vip_info(test_user_id)
        if updated_info:
            print(f"   - تاريخ الانتهاء الجديد: {updated_info['expires_at']}")
            print(f"   - إجمالي الأيام الجديد: {updated_info['days_total']}")
    else:
        print("❌ فشل في تمديد العضوية")
        return False
    
    # اختبار الإحصائيات
    print(f"\n📈 اختبار الإحصائيات...")
    stats = vip_manager.get_vip_statistics()
    print(f"✅ الإحصائيات:")
    print(f"   - إجمالي الأعضاء: {stats['total']}")
    print(f"   - الأعضاء النشطين: {stats['active']}")
    print(f"   - العضويات المنتهية: {stats['expired']}")
    print(f"   - تنتهي قريباً: {stats['expiring_soon']}")
    
    # اختبار التحقق من صلاحيات المشرف
    print(f"\n👑 اختبار صلاحيات المشرف...")
    is_admin = auth_manager.is_admin(admin_ids[0])
    if is_admin:
        print(f"✅ المستخدم {admin_ids[0]} هو مشرف")
    else:
        print(f"❌ المستخدم {admin_ids[0]} ليس مشرف")
        return False
    
    # اختبار التحقق من التفويض
    print(f"\n🔐 اختبار التفويض...")
    is_authorized = auth_manager.is_authorized_user(test_user_id)
    if is_authorized:
        print(f"✅ المستخدم {test_user_id} مخول لاستخدام البوت")
    else:
        print(f"❌ المستخدم {test_user_id} غير مخول")
        return False
    
    # اختبار حذف العضو
    print(f"\n🗑️ اختبار حذف العضو...")
    result = vip_manager.remove_vip(test_user_id)
    if result:
        print(f"✅ تم حذف العضو {test_user_id} بنجاح")
        
        # التحقق من الحذف
        is_vip_after_delete = vip_manager.is_vip(test_user_id, admin_ids)
        if not is_vip_after_delete:
            print(f"✅ تأكيد: المستخدم {test_user_id} لم يعد عضو VIP")
        else:
            print(f"❌ خطأ: المستخدم {test_user_id} ما زال عضو VIP")
            return False
    else:
        print("❌ فشل في حذف العضو")
        return False
    
    # تنظيف ملف الاختبار
    try:
        if os.path.exists(test_vip_file):
            os.remove(test_vip_file)
            print(f"🧹 تم حذف ملف الاختبار: {test_vip_file}")
    except Exception as e:
        print(f"⚠️ تحذير: فشل في حذف ملف الاختبار: {e}")
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("🌟 اختبار نظام العضوية المميزة (VIP)")
    print("=" * 50)
    
    try:
        success = test_vip_system()
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 جميع الاختبارات نجحت!")
            print("✅ نظام VIP يعمل بشكل صحيح")
            print("🚀 النظام جاهز للاستخدام في البوت")
        else:
            print("\n" + "=" * 50)
            print("❌ فشل في بعض الاختبارات")
            print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
