#!/usr/bin/env python3
"""
اختبار ميزة إزالة العلامة المائية
"""

import os
import sys
import tempfile
import numpy as np
import cv2
from datetime import datetime

def create_test_video_with_watermark(output_path, duration=5, fps=30):
    """إنشاء فيديو تجريبي مع علامة مائية"""
    try:
        # إعدادات الفيديو
        width, height = 640, 480
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        total_frames = duration * fps
        
        for frame_num in range(total_frames):
            # إنشاء إطار ملون
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # خلفية متدرجة
            for y in range(height):
                for x in range(width):
                    frame[y, x] = [
                        int(255 * x / width),
                        int(255 * y / height), 
                        int(255 * (frame_num % 30) / 30)
                    ]
            
            # إضافة علامة مائية في الزاوية اليمنى العلوية
            watermark_text = "TEST WATERMARK"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            color = (255, 255, 255)  # أبيض
            thickness = 2
            
            # حساب حجم النص
            text_size = cv2.getTextSize(watermark_text, font, font_scale, thickness)[0]
            
            # موضع النص (الزاوية اليمنى العلوية)
            text_x = width - text_size[0] - 10
            text_y = text_size[1] + 10
            
            # إضافة خلفية للنص
            cv2.rectangle(frame, 
                         (text_x - 5, text_y - text_size[1] - 5),
                         (text_x + text_size[0] + 5, text_y + 5),
                         (0, 0, 0), -1)
            
            # إضافة النص
            cv2.putText(frame, watermark_text, (text_x, text_y), 
                       font, font_scale, color, thickness)
            
            # إضافة شعار دائري في الزاوية اليسرى السفلية
            center = (50, height - 50)
            radius = 30
            cv2.circle(frame, center, radius, (0, 255, 255), -1)
            cv2.putText(frame, "LOGO", (center[0] - 20, center[1] + 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            out.write(frame)
        
        out.release()
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء الفيديو التجريبي: {e}")
        return False

def test_watermark_detection():
    """اختبار كشف العلامة المائية"""
    try:
        from video_watermark_remover import WatermarkRemover
        
        print("🧪 اختبار كشف العلامة المائية...")
        
        # إنشاء فيديو تجريبي
        temp_dir = tempfile.mkdtemp()
        test_video = os.path.join(temp_dir, "test_video.mp4")
        
        print("📹 إنشاء فيديو تجريبي...")
        if not create_test_video_with_watermark(test_video):
            print("❌ فشل في إنشاء الفيديو التجريبي")
            return False
        
        print("✅ تم إنشاء الفيديو التجريبي")
        
        # اختبار كشف العلامة المائية
        remover = WatermarkRemover()
        
        # قراءة الإطار الأول
        cap = cv2.VideoCapture(test_video)
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            print("❌ فشل في قراءة الفيديو")
            return False
        
        # كشف العلامة المائية
        region = remover.detect_watermark_region(frame)
        
        if region:
            print(f"✅ تم كشف علامة مائية في المنطقة: {region}")
            
            # اختبار الطمس
            print("🔄 اختبار طمس العلامة المائية...")
            blurred_frame = remover.blur_watermark_region(frame, region)
            
            # اختبار الإزالة الذكية
            print("🔄 اختبار الإزالة الذكية...")
            inpainted_frame = remover.remove_watermark_inpainting(frame, region)
            
            print("✅ تم اختبار طرق المعالجة بنجاح")
            
        else:
            print("⚠️ لم يتم كشف علامة مائية")
        
        # تنظيف
        remover.cleanup()
        if os.path.exists(test_video):
            os.remove(test_video)
        if os.path.exists(temp_dir):
            os.rmdir(temp_dir)
        
        return True
        
    except ImportError:
        print("❌ لم يتم العثور على وحدة إزالة العلامة المائية")
        print("💡 تأكد من وجود ملف video_watermark_remover.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_video_processing():
    """اختبار معالجة الفيديو الكاملة"""
    try:
        from video_watermark_remover import remove_video_watermark
        
        print("🎬 اختبار معالجة الفيديو الكاملة...")
        
        # إنشاء فيديو تجريبي
        temp_dir = tempfile.mkdtemp()
        input_video = os.path.join(temp_dir, "input_test.mp4")
        output_video = os.path.join(temp_dir, "output_test.mp4")
        
        print("📹 إنشاء فيديو تجريبي...")
        if not create_test_video_with_watermark(input_video, duration=3):
            print("❌ فشل في إنشاء الفيديو التجريبي")
            return False
        
        # معالجة الفيديو
        print("⏳ معالجة الفيديو...")
        start_time = datetime.now()
        
        success = remove_video_watermark(input_video, output_video, method="blur")
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        if success and os.path.exists(output_video):
            input_size = os.path.getsize(input_video)
            output_size = os.path.getsize(output_video)
            
            print(f"✅ تم معالجة الفيديو بنجاح!")
            print(f"⏱️ وقت المعالجة: {processing_time:.2f} ثانية")
            print(f"📊 حجم الملف الأصلي: {input_size / 1024:.1f} KB")
            print(f"📊 حجم الملف المعالج: {output_size / 1024:.1f} KB")
            
            # تنظيف
            os.remove(input_video)
            os.remove(output_video)
            os.rmdir(temp_dir)
            
            return True
        else:
            print("❌ فشل في معالجة الفيديو")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المعالجة: {e}")
        return False

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("📦 فحص المكتبات المطلوبة...")
    
    dependencies = {
        "cv2": "opencv-python",
        "numpy": "numpy",
        "PIL": "Pillow", 
        "moviepy": "moviepy"
    }
    
    missing = []
    
    for module, package in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
        print("💡 لتثبيتها:")
        for package in missing:
            print(f"   pip install {package}")
        return False
    
    print("✅ جميع المكتبات متوفرة")
    return True

def main():
    print("🎬 اختبار ميزة إزالة العلامة المائية")
    print("=" * 50)
    
    # فحص المكتبات
    if not check_dependencies():
        print("\n❌ يرجى تثبيت المكتبات المفقودة أولاً")
        return
    
    print("\n" + "=" * 50)
    
    # اختبار كشف العلامة المائية
    if test_watermark_detection():
        print("✅ اختبار كشف العلامة المائية نجح")
    else:
        print("❌ اختبار كشف العلامة المائية فشل")
        return
    
    print("\n" + "=" * 50)
    
    # اختبار معالجة الفيديو
    if test_video_processing():
        print("✅ اختبار معالجة الفيديو نجح")
    else:
        print("❌ اختبار معالجة الفيديو فشل")
        return
    
    print("\n" + "=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("🚀 ميزة إزالة العلامة المائية جاهزة للاستخدام")

if __name__ == "__main__":
    main()
