"""
إعدادات متقدمة لميزة إزالة العلامة المائية
"""

# إعدادات كشف العلامة المائية
DETECTION_SETTINGS = {
    # حد التباين الأدنى لكشف العلامة المائية
    "min_variance_threshold": 100,
    
    # نسب المناطق المراد فحصها (من 0 إلى 1)
    "detection_regions": {
        "top_right": (0.7, 0, 1.0, 0.3),      # الزاوية اليمنى العلوية
        "top_left": (0, 0, 0.3, 0.3),         # الزاوية اليسرى العلوية  
        "bottom_right": (0.7, 0.7, 1.0, 1.0), # الزاوية اليمنى السفلية
        "bottom_left": (0, 0.7, 0.3, 1.0),    # الزاوية اليسرى السفلية
        "top_center": (0.3, 0, 0.7, 0.2),     # الوسط العلوي
        "bottom_center": (0.3, 0.8, 0.7, 1.0) # الوسط السفلي
    },
    
    # أولوية المناطق (الأعلى رقماً = أولوية أكبر)
    "region_priority": {
        "top_right": 10,
        "bottom_right": 9,
        "top_left": 8,
        "bottom_left": 7,
        "top_center": 6,
        "bottom_center": 5
    }
}

# إعدادات معالجة الفيديو
PROCESSING_SETTINGS = {
    # إعدادات الطمس
    "blur": {
        "kernel_size": (15, 15),  # حجم نواة الطمس
        "sigma_x": 0,             # انحراف معياري في الاتجاه X
        "sigma_y": 0              # انحراف معياري في الاتجاه Y
    },
    
    # إعدادات الإزالة الذكية
    "inpaint": {
        "radius": 3,                    # نصف قطر الإزالة
        "method": "TELEA",              # طريقة الإزالة (TELEA أو NS)
        "flags": None                   # معاملات إضافية
    },
    
    # إعدادات الفيديو
    "video": {
        "codec": "mp4v",               # ترميز الفيديو
        "quality": 1.0,                # جودة الفيديو (0-1)
        "preserve_fps": True,          # الحفاظ على معدل الإطارات
        "preserve_resolution": True    # الحفاظ على الدقة
    }
}

# إعدادات الأداء
PERFORMANCE_SETTINGS = {
    # حدود الذاكرة
    "max_memory_usage": 512 * 1024 * 1024,  # 512 ميجابايت
    
    # حدود المعالجة
    "max_processing_time": 300,  # 5 دقائق كحد أقصى
    "progress_update_interval": 30,  # تحديث التقدم كل 30 إطار
    
    # إعدادات التنظيف
    "cleanup_temp_files": True,
    "temp_dir_prefix": "watermark_removal_",
    
    # إعدادات التسجيل
    "log_processing_time": True,
    "log_file_sizes": True,
    "log_detection_results": True
}

# إعدادات متقدمة للكشف
ADVANCED_DETECTION = {
    # استخدام تقنيات متقدمة للكشف
    "use_edge_detection": True,
    "use_contour_analysis": True,
    "use_template_matching": False,
    
    # معاملات كشف الحواف
    "edge_detection": {
        "low_threshold": 50,
        "high_threshold": 150,
        "aperture_size": 3
    },
    
    # معاملات تحليل الكنتور
    "contour_analysis": {
        "min_contour_area": 100,
        "max_contour_area": 10000,
        "approximation_epsilon": 0.02
    }
}

# قوالب العلامات المائية الشائعة
WATERMARK_TEMPLATES = {
    # أنماط نصية شائعة
    "text_patterns": [
        "watermark", "علامة مائية", "حقوق محفوظة",
        "copyright", "©", "®", "™"
    ],
    
    # أحجام شائعة للعلامات المائية (نسبة من حجم الفيديو)
    "common_sizes": [
        (0.1, 0.05),   # صغير
        (0.2, 0.1),    # متوسط
        (0.3, 0.15)    # كبير
    ],
    
    # ألوان شائعة للعلامات المائية
    "common_colors": [
        (255, 255, 255),  # أبيض
        (0, 0, 0),        # أسود
        (255, 255, 0),    # أصفر
        (0, 255, 255)     # سماوي
    ]
}

# إعدادات التحقق من الجودة
QUALITY_SETTINGS = {
    # التحقق من جودة النتيجة
    "enable_quality_check": True,
    
    # معايير الجودة
    "quality_metrics": {
        "min_psnr": 25.0,      # نسبة الإشارة للضوضاء
        "min_ssim": 0.8,       # مؤشر التشابه الهيكلي
        "max_mse": 1000.0      # متوسط مربع الخطأ
    },
    
    # إجراءات في حالة فشل الجودة
    "fallback_on_quality_fail": True,
    "quality_fail_method": "blur"  # استخدام الطمس كبديل
}

# إعدادات التخصيص حسب نوع المحتوى
CONTENT_SPECIFIC_SETTINGS = {
    # إعدادات للأفلام والمسلسلات
    "movies_tv": {
        "detection_sensitivity": 0.8,
        "preferred_method": "inpaint",
        "region_expansion": 1.2
    },
    
    # إعدادات للمحتوى الرياضي
    "sports": {
        "detection_sensitivity": 0.9,
        "preferred_method": "blur",
        "region_expansion": 1.1
    },
    
    # إعدادات للأخبار
    "news": {
        "detection_sensitivity": 0.7,
        "preferred_method": "inpaint",
        "region_expansion": 1.0
    }
}

def get_detection_regions(width, height):
    """حساب المناطق الفعلية للكشف بناءً على أبعاد الفيديو"""
    regions = {}
    
    for name, (x1_ratio, y1_ratio, x2_ratio, y2_ratio) in DETECTION_SETTINGS["detection_regions"].items():
        x1 = int(width * x1_ratio)
        y1 = int(height * y1_ratio)
        x2 = int(width * x2_ratio)
        y2 = int(height * y2_ratio)
        
        regions[name] = (x1, y1, x2, y2)
    
    return regions

def get_processing_config(method="blur"):
    """الحصول على إعدادات المعالجة حسب الطريقة"""
    if method in PROCESSING_SETTINGS:
        return PROCESSING_SETTINGS[method]
    else:
        return PROCESSING_SETTINGS["blur"]  # افتراضي

def optimize_settings_for_video(width, height, duration, file_size):
    """تحسين الإعدادات حسب خصائص الفيديو"""
    optimized = {}
    
    # تحسين حسب الحجم
    if file_size > 100 * 1024 * 1024:  # أكبر من 100 ميجابايت
        optimized["method"] = "blur"  # استخدام الطمس للسرعة
        optimized["quality"] = 0.8
    else:
        optimized["method"] = "inpaint"  # استخدام الإزالة الذكية
        optimized["quality"] = 1.0
    
    # تحسين حسب الدقة
    if width * height > 1920 * 1080:  # أعلى من Full HD
        optimized["processing_scale"] = 0.5  # تقليل الحجم للمعالجة
    else:
        optimized["processing_scale"] = 1.0
    
    # تحسين حسب المدة
    if duration > 300:  # أكثر من 5 دقائق
        optimized["sample_frames"] = True  # معالجة عينة من الإطارات
        optimized["sample_rate"] = 0.1
    else:
        optimized["sample_frames"] = False
    
    return optimized

# إعدادات التصدير
EXPORT_SETTINGS = {
    "default_format": "mp4",
    "supported_formats": ["mp4", "avi", "mov", "mkv"],
    "compression_level": "medium",  # low, medium, high
    "preserve_metadata": True
}
