#!/usr/bin/env python3
"""
سكريبت تثبيت المكتبات المطلوبة لميزة إزالة العلامة المائية
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة باستخدام pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """التحقق من وجود مكتبة"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print("🎬 تثبيت مكتبات ميزة إزالة العلامة المائية")
    print("=" * 50)
    
    # قائمة المكتبات المطلوبة
    packages = {
        "opencv-python": "cv2",
        "numpy": "numpy", 
        "Pillow": "PIL",
        "moviepy": "moviepy"
    }
    
    installed_count = 0
    failed_count = 0
    
    for pip_name, import_name in packages.items():
        print(f"\n📦 فحص {pip_name}...")
        
        if check_package(import_name):
            print(f"✅ {pip_name} مثبت بالفعل")
            installed_count += 1
        else:
            print(f"⏳ تثبيت {pip_name}...")
            if install_package(pip_name):
                print(f"✅ تم تثبيت {pip_name} بنجاح")
                installed_count += 1
            else:
                print(f"❌ فشل في تثبيت {pip_name}")
                failed_count += 1
    
    print("\n" + "=" * 50)
    print("📊 ملخص التثبيت:")
    print(f"✅ مثبت بنجاح: {installed_count}")
    print(f"❌ فشل في التثبيت: {failed_count}")
    
    if failed_count == 0:
        print("\n🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("🚀 يمكنك الآن تشغيل البوت مع ميزة إزالة العلامة المائية")
        
        # اختبار سريع
        print("\n🧪 اختبار سريع...")
        try:
            import cv2
            import numpy as np
            from PIL import Image
            import moviepy.editor
            print("✅ جميع المكتبات تعمل بشكل صحيح!")
        except Exception as e:
            print(f"⚠️ خطأ في الاختبار: {e}")
    else:
        print("\n⚠️ بعض المكتبات لم يتم تثبيتها بنجاح")
        print("💡 جرب تثبيتها يدوياً:")
        for pip_name, import_name in packages.items():
            if not check_package(import_name):
                print(f"   pip install {pip_name}")

if __name__ == "__main__":
    main()
